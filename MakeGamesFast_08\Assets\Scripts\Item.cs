using System;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Image))]
public class Item : MonoBehaviour
{
    private string itemName;
    public string ItemName
    {
        get { return itemName; }
        set { itemName = value; UpdateNameText(); }
    }
    public Vector2Int size = Vector2Int.one;
    public Vector2Int Size
    {
        get { return size; }
        set { size = value; UpdateSize(); }
    }
    private Image image;
    private float price;
    private float Price
    {
        get { return price; }
        set { price = value; UpdatePriceText(); }
    }


    public InventoryGrid InventoryGrid { get; private set; }
    private TextMeshProUGUI priceText;
    private TextMeshProUGUI nameText;

    private void UpdateNameText()
    {
        nameText.text = ItemName;
    }

    private void UpdatePriceText()
    {
        priceText.text = Price.ToString();
    }

    [Button]
    private void UpdateSize()
    {
        RectTransform rectTransform = this.transform as RectTransform;
        float width = InventoryStats.CELL_SIZE * Size.x + (InventoryStats.CELL_GAP * (Size.x - 1));
        float height = InventoryStats.CELL_SIZE * Size.y + (InventoryStats.CELL_GAP * (Size.y - 1));
        rectTransform.sizeDelta = new Vector2(width, height);
    }
    public void SetInventoryGrid(InventoryGrid inventoryGrid)
    {
        InventoryGrid = inventoryGrid;
    }

    public class Builder
    {
        public string Name;
        public Vector2Int Size = Vector2Int.one;
        public float Price;
        public Transform Parent = null;

        public Builder(string name, Vector2Int size, float price)
        {
            Name = name;
            Size = size;
            this.Price = price;
        }

        public Builder WithParent(Transform parent)
        {
            Parent = parent;
            return this;
        }

        public Item Build()
        {
            GameObject itemObject = new GameObject("Item_" + Name, typeof(RectTransform));
            RectTransform itemObjectRectTransform = itemObject.transform as RectTransform;
            itemObjectRectTransform.SetParent(Parent);
            itemObjectRectTransform.pivot = new Vector2(0.0f, 1.0f);
            itemObjectRectTransform.localScale = Vector3.one;
            LayoutRebuilder.ForceRebuildLayoutImmediate(itemObjectRectTransform);

            Item item = itemObject.AddComponent<Item>();

            Image itemImage = item.GetComponent<Image>();
            itemImage.color = Color.green;
            item.image = itemImage;

            GameObject priceText = new GameObject("Price", typeof(RectTransform), typeof(TextMeshProUGUI), typeof(ContentSizeFitter));
            TextMeshProUGUI priceTextComponent = priceText.GetComponent<TextMeshProUGUI>();
            priceTextComponent.fontSize = 10;
            ContentSizeFitter priceTextContentSizeFitter = priceText.GetComponent<ContentSizeFitter>();
            priceTextContentSizeFitter.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;
            priceTextContentSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
            RectTransform priceTextRectTransform = priceText.transform as RectTransform;
            priceTextRectTransform.SetParent(item.transform);
            priceTextRectTransform.pivot = new Vector2(1, 1);
            priceTextRectTransform.anchorMin = new Vector2(1, 1);
            priceTextRectTransform.anchorMax = new Vector2(1, 1);
            //LayoutRebuilder.ForceRebuildLayoutImmediate(priceTextRectTransform);


            GameObject nameText = new GameObject("Name", typeof(RectTransform), typeof(TextMeshProUGUI), typeof(ContentSizeFitter));
            TextMeshProUGUI nameTextTextComponent = nameText.GetComponent<TextMeshProUGUI>();
            nameTextTextComponent.fontSize = 10;
            ContentSizeFitter nameTextContentSizeFitter = nameText.GetComponent<ContentSizeFitter>();
            nameTextContentSizeFitter.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;
            nameTextContentSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
            RectTransform nameTextRectTransform = nameText.transform as RectTransform;
            nameTextRectTransform.SetParent(item.transform);
            nameTextRectTransform.pivot = new Vector2(0, 0);
            nameTextRectTransform.anchorMin = new Vector2(0, 0);
            nameTextRectTransform.anchorMax = new Vector2(0, 0);
            //LayoutRebuilder.ForceRebuildLayoutImmediate(nameTextRectTransform);

            Canvas.ForceUpdateCanvases();
            priceTextRectTransform.localPosition = Vector3.zero;
            priceTextRectTransform.localScale = Vector3.one;
            nameTextRectTransform.localPosition = Vector3.zero;
            nameTextRectTransform.localScale = Vector3.one;


            item.nameText = nameTextTextComponent;
            item.priceText = priceTextComponent;
            item.ItemName = Name;
            item.Size = Size;
            item.Price = Price;


            return item;
        }
    }
}